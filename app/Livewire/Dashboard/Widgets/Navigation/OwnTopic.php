<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Traits\LivewireGeneralFunctions;
use App\Services\Calculation\FrequencyService;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Http\Request;

class OwnTopic extends Component
{
   use LivewireGeneralFunctions;

    public $userDetails;
    public $topicText = '';
    public $frequency = 0;
    public $timeDisplay = '';
    public $timeSeconds = 0;
    public $showFrequency = false;
    public $showTime = false;
    public $isCalculating = false;
    public $farbklang = false;

    // Widget properties
    public $poolId;
    public $widget;

    // Toggle functionality
    public $showFrequencyGenerator = false;

    protected $listeners = ['toggleFrequencyGenerator'];

    protected $rules = [
        'topicText' => 'nullable|string|max:1000',
    ];

    /**
     * Get FrequencyService from container
     *
     * @return FrequencyService
     */
    protected function getFrequencyService(): FrequencyService
    {
        return app(FrequencyService::class);
    }

    /**
     * Toggle between own topic and frequency generator
     *
     * @param array $data
     * @return void
     */
    public function toggleFrequencyGenerator($data): void
    {
        Log::info('Toggle method called', [
            'data' => $data,
            'widget_id' => $this->widget['id'] ?? 'no-widget-id',
            'current_state' => $this->showFrequencyGenerator
        ]);

        // Only toggle if this is the correct widget
        if (isset($data['widgetId']) && $data['widgetId'] == ($this->widget['id'] ?? 'default')) {
            $this->showFrequencyGenerator = !$this->showFrequencyGenerator;

            Log::info('Toggle successful', [
                'widget_id' => $this->widget['id'],
                'new_state' => $this->showFrequencyGenerator
            ]);
        } else {
            Log::info('Toggle skipped - widget ID mismatch', [
                'received_id' => $data['widgetId'] ?? 'no-id',
                'expected_id' => $this->widget['id'] ?? 'no-widget-id'
            ]);
        }
    }

    public function mount($poolId = null, $widget = null)
    {
        $this->poolId = $poolId;
        $this->widget = $widget;

        $this->userDetails = getUserDetails();
        $this->topicText = $this->userDetails->thema_speichern ?? '';
        // Initialize frequency and time if topic exists
        if (!empty($this->topicText)) {
            $this->updateFrequencyFromTopic();
        }
    }

    public function updatedTopicText()
    {
        $this->calculateFrequencyIfNeeded();
    }

    public function calculateFrequencyIfNeeded()
    {
        // Only update frequency if text is not empty and has meaningful content
        if (!empty(trim($this->topicText)) && strlen(trim($this->topicText)) > 2) {
            $this->updateFrequencyFromTopic();
        } else {
            // Reset frequency display for empty or very short text
            $this->frequency = 0;
            $this->timeSeconds = 0;
            $this->timeDisplay = '';
            $this->showFrequency = false;
            $this->showTime = false;
        }
    }

    protected function updateFrequencyFromTopic()
    {
        // Don't recalculate if already calculating
        if ($this->isCalculating) {
            return;
        }

        $this->isCalculating = true;

        try {
            // Calculate frequency using FrequencyService
            $this->frequency = $this->getFrequencyService()->calculateFrequencySafely($this->topicText, [
                'apply_transformations' => true,
                'fallback_frequency' => 250
            ]);

            if ($this->frequency > 0) {
                // Generate random time using FrequencyService
                $this->timeSeconds = $this->getFrequencyService()->generateRandomTime();
                $this->timeDisplay = gmdate('i:s', $this->timeSeconds);
                $this->showFrequency = true;
                $this->showTime = true;
            } else {
                $this->timeSeconds = 0;
                $this->timeDisplay = '';
                $this->showFrequency = false;
                $this->showTime = false;
            }
        } catch (\Exception $e) {
            Log::error('Topic frequency calculation failed', [
                'error' => $e->getMessage(),
                'topic' => substr($this->topicText, 0, 50) . '...'
            ]);
            // Don't show error toastr during typing, just log it
        } finally {
            $this->isCalculating = false;
        }
    }

    protected function isTopicTextEmpty(): bool
    {
        return empty(trim($this->topicText));
    }

    protected function rateLimitKey(): string
    {
        return 'cart_add_' . getUserId(); // assumes getUserId() is globally available
    }

    protected function hasExceededRateLimit(): bool
    {
        return RateLimiter::tooManyAttempts($this->rateLimitKey(), 10);
    }

    protected function ensureFrequencyIsSet(): void
    {
        if ($this->frequency <= 0) {
            $this->frequency = $this->calculateFrequencySafely($this->topicText);
        }
    }

    protected function ensureTimeSecondsIsSet(): void
    {
        if ($this->timeSeconds <= 0) {
            $details = biorythVisibleDetails();
            $this->timeSeconds = $details
                ? rand($details->gs_min_price, $details->gs_max_price)
                : rand(60, 300);
        }
    }

    protected function prepareCartData(): Request
    {
        return new Request([
            'ana_id'      => 1,
            'name'        => trim($this->topicText),
            'submenu_id'  => '',
            'proID'       => '',
            'calculation' => '',
            'male'        => '',
            'heart'       => '',
            'price'       => (int) $this->timeSeconds,
            'causes_id'   => '',
            'medium_id'   => '',
            'tipp_id'     => '',
            'color'       => '',
            'type'        => 'Topic',
            'frequency'   => (int) $this->frequency,
            'time'        => (int) $this->timeSeconds,
        ]);
    }

    protected function warnUser(string $messageKey): void
    {
        $this->showToastr('warning', trans('action.warning'), trans("action.$messageKey"));
    }

    protected function successUser(string $messageKey): void
    {
        $this->showToastr('success', trans('action.success'), trans("action.$messageKey"));
    }

    protected function errorUser(string $messageKey): void
    {
        $this->showToastr('error', trans('action.error'), trans("action.$messageKey"));
    }

    protected function logCartError(\Throwable $e): void
    {
        Log::error('Cart addition failed', [
            'error' => $e->getMessage(),
            'topic' => substr($this->topicText, 0, 50) . '...',
            'trace' => $e->getTraceAsString(),
        ]);
    }

    public function addToCart(): void
    {
        if ($this->isTopicTextEmpty()) {
            $this->warnUser('no_product_title');
            return;
        }

        if ($this->hasExceededRateLimit()) {
            $this->warnUser('cart_max_allow_alert');
            return;
        }

        RateLimiter::hit($this->rateLimitKey(), 60);

        $this->ensureFrequencyIsSet();
        $this->ensureTimeSecondsIsSet();

        try {
            $response = $this->getFrequencyService()->submitToSajaxController($this->prepareCartData());

            $data = $response->getData(true);

            if ($data['success'] ?? false) {
                $this->successUser('added_successfully');
                $this->dispatch('cartUpdated');
            } else {
                $this->errorUser($data['message'] ?? 'cart_add_failed');
            }
        } catch (\Throwable $e) {
            $this->logCartError($e);
            $this->errorUser('cart_add_failed');
        }
    }








    public function saveTopic()
    {
        try {
            $updated = DB::table('users')
                ->where('id', getUserId())
                ->update(['thema_speichern' => $this->topicText]);

            if ($updated) {
                $this->showToastr('success', trans('action.success'), trans('action.topic_saved_successfully'));
                // Update userDetails to reflect the change
                $this->userDetails = getUserDetails();
            } else {
                $this->showToastr('error', trans('action.error'), trans('action.topic_save_failed'));
            }
        } catch (\Exception $e) {
            Log::error('Topic save failed', [
                'error' => $e->getMessage(),
                'user_id' => getUserId()
            ]);
            $this->showToastr('error', trans('action.error'), trans('action.topic_save_failed'));
        }
    }

    public function deleteTopic()
    {
        try {
            $this->topicText = '';
            $updated = DB::table('users')
                ->where('id', getUserId())
                ->update(['thema_speichern' => '']);

            if ($updated) {
                $this->frequency = 0;
                $this->timeSeconds = 0;
                $this->timeDisplay = '';
                $this->showFrequency = false;
                $this->showTime = false;
                $this->showToastr('success', trans('action.success'), trans('action.topic_deleted_successfully'));
                // Update userDetails to reflect the change
                $this->userDetails = getUserDetails();
            } else {
                $this->showToastr('error', trans('action.error'), trans('action.topic_delete_failed'));
            }
        } catch (\Exception $e) {
            Log::error('Topic delete failed', [
                'error' => $e->getMessage(),
                'user_id' => getUserId()
            ]);
            $this->showToastr('error', trans('action.error'), trans('action.topic_delete_failed'));
        }
    }
    
    public function render()
    {
        return view('livewire.dashboard.widgets.navigation.own-topic');
    }
}
