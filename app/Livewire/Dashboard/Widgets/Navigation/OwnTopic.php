<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Traits\LivewireGeneralFunctions;
use App\Services\Calculation\FrequencyService;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class OwnTopic extends Component
{
   use LivewireGeneralFunctions;

    public $userDetails;
    public $topicText = '';
    public $frequency = 0;
    public $timeDisplay = '';
    public $timeSeconds = 0;
    public $showFrequency = false;
    public $showTime = false;
    public $isCalculating = false;
    public $farbklang = false;

    // Toggle functionality
    public $mode = 'own_topic'; // 'own_topic' or 'frequency_generator'

    // Frequency Generator specific properties
    public $frequencyHz = '';
    public $frequencyTime = '';
    public $calculatedFrequency = 0;
    public $randomTime = 30;
    public $biorythDetails = null;

    protected $rules = [
        'topicText' => 'nullable|string|max:1000',
    ];

    /**
     * Get dynamic validation rules based on current mode
     *
     * @return array
     */
    protected function getValidationRules(): array
    {
        if ($this->mode === 'frequency_generator') {
            return [
                'topicText' => 'required|string|min:1|max:500',
                'frequencyHz' => 'required|numeric|min:250|max:20000',
                'frequencyTime' => 'required|numeric|min:5|max:3600'
            ];
        }

        return [
            'topicText' => 'nullable|string|max:1000',
        ];
    }

    /**
     * Get FrequencyService from container
     *
     * @return FrequencyService
     */
    protected function getFrequencyService(): FrequencyService
    {
        return app(FrequencyService::class);
    }

    /**
     * Toggle between own topic and frequency generator modes
     *
     * @param string $newMode
     * @return void
     */
    public function switchMode(string $newMode): void
    {
        if (!in_array($newMode, ['own_topic', 'frequency_generator'])) {
            return;
        }

        $this->mode = $newMode;

        if ($newMode === 'frequency_generator') {
            $this->initializeFrequencyGeneratorMode();
        } else {
            $this->initializeOwnTopicMode();
        }
    }

    /**
     * Initialize frequency generator mode
     *
     * @return void
     */
    protected function initializeFrequencyGeneratorMode(): void
    {
        // Get biorhythm details if not already loaded
        if (!$this->biorythDetails) {
            $this->biorythDetails = biorythVisibleDetails();
        }

        // Generate random time within bounds using FrequencyService
        $this->randomTime = $this->getFrequencyService()->generateRandomTime($this->biorythDetails);

        // Calculate frequency if topic exists
        if (!empty($this->topicText)) {
            $this->calculatedFrequency = $this->getFrequencyService()->calculateFrequencySafely($this->topicText);
            $this->frequencyHz = $this->calculatedFrequency > 0 ? (string)$this->calculatedFrequency : '';
        }

        $this->frequencyTime = (string)$this->randomTime;
    }

    /**
     * Initialize own topic mode
     *
     * @return void
     */
    protected function initializeOwnTopicMode(): void
    {
        // Reset frequency generator specific properties
        $this->frequencyHz = '';
        $this->frequencyTime = '';

        // Recalculate frequency for own topic mode if needed
        if (!empty($this->topicText)) {
            $this->updateFrequencyFromTopic();
        }
    }

    public function mount()
    {
        $this->userDetails = getUserDetails();
        $this->topicText = $this->userDetails->thema_speichern ?? '';

        // Get biorhythm details for frequency generator mode
        $this->biorythDetails = biorythVisibleDetails();

        // Initialize frequency and time if topic exists
        if (!empty($this->topicText)) {
            $this->updateFrequencyFromTopic();
        }

        // Initialize frequency generator properties
        $this->initializeFrequencyGeneratorMode();
    }

    public function updatedTopicText()
    {
        if ($this->mode === 'frequency_generator') {
            $this->updateFrequencyFromTopicForGenerator();
        } else {
            $this->calculateFrequencyIfNeeded();
        }
    }

    /**
     * Update frequency for frequency generator mode
     *
     * @return void
     */
    protected function updateFrequencyFromTopicForGenerator(): void
    {
        $userId = getUserId();

        $this->getFrequencyService()->updateFrequencyFromTopic(
            $this->topicText,
            $userId,
            function($frequency) {
                $this->frequencyHz = (string)$frequency;
                // Also generate new random time when frequency is updated
                $this->randomTime = $this->getFrequencyService()->generateRandomTime($this->biorythDetails);
                $this->frequencyTime = (string)$this->randomTime;
            },
            function($result) {
                if ($result['rate_limited']) {
                    $this->showToastr('warning', trans('action.frequency_generator'), $result['message']);
                }
            }
        );
    }

    public function calculateFrequencyIfNeeded()
    {
        // Only update frequency if text is not empty and has meaningful content
        if (!empty(trim($this->topicText)) && strlen(trim($this->topicText)) > 2) {
            $this->updateFrequencyFromTopic();
        } else {
            // Reset frequency display for empty or very short text
            $this->frequency = 0;
            $this->timeSeconds = 0;
            $this->timeDisplay = '';
            $this->showFrequency = false;
            $this->showTime = false;
        }
    }

    protected function updateFrequencyFromTopic()
    {
        // Don't recalculate if already calculating
        if ($this->isCalculating) {
            return;
        }

        $this->isCalculating = true;

        try {
            // Calculate frequency using FrequencyService
            $this->frequency = $this->getFrequencyService()->calculateFrequencySafely($this->topicText, [
                'apply_transformations' => true,
                'fallback_frequency' => 250
            ]);

            if ($this->frequency > 0) {
                // Generate random time using FrequencyService
                $this->timeSeconds = $this->getFrequencyService()->generateRandomTime();
                $this->timeDisplay = gmdate('i:s', $this->timeSeconds);
                $this->showFrequency = true;
                $this->showTime = true;
            } else {
                $this->timeSeconds = 0;
                $this->timeDisplay = '';
                $this->showFrequency = false;
                $this->showTime = false;
            }
        } catch (\Exception $e) {
            Log::error('Topic frequency calculation failed', [
                'error' => $e->getMessage(),
                'topic' => substr($this->topicText, 0, 50) . '...'
            ]);
            // Don't show error toastr during typing, just log it
        } finally {
            $this->isCalculating = false;
        }
    }

    protected function isTopicTextEmpty(): bool
    {
        return empty(trim($this->topicText));
    }

    protected function rateLimitKey(): string
    {
        return 'cart_add_' . getUserId(); // assumes getUserId() is globally available
    }

    protected function hasExceededRateLimit(): bool
    {
        return RateLimiter::tooManyAttempts($this->rateLimitKey(), 10);
    }

    protected function ensureFrequencyIsSet(): void
    {
        if ($this->frequency <= 0) {
            $this->frequency = $this->calculateFrequencySafely($this->topicText);
        }
    }

    protected function ensureTimeSecondsIsSet(): void
    {
        if ($this->timeSeconds <= 0) {
            $details = biorythVisibleDetails();
            $this->timeSeconds = $details
                ? rand($details->gs_min_price, $details->gs_max_price)
                : rand(60, 300);
        }
    }

    protected function prepareCartData(): Request
    {
        return new Request([
            'ana_id'      => 1,
            'name'        => trim($this->topicText),
            'submenu_id'  => '',
            'proID'       => '',
            'calculation' => '',
            'male'        => '',
            'heart'       => '',
            'price'       => (int) $this->timeSeconds,
            'causes_id'   => '',
            'medium_id'   => '',
            'tipp_id'     => '',
            'color'       => '',
            'type'        => 'Topic',
            'frequency'   => (int) $this->frequency,
            'time'        => (int) $this->timeSeconds,
        ]);
    }

    protected function warnUser(string $messageKey): void
    {
        $this->showToastr('warning', trans('action.warning'), trans("action.$messageKey"));
    }

    protected function successUser(string $messageKey): void
    {
        $this->showToastr('success', trans('action.success'), trans("action.$messageKey"));
    }

    protected function errorUser(string $messageKey): void
    {
        $this->showToastr('error', trans('action.error'), trans("action.$messageKey"));
    }

    protected function logCartError(\Throwable $e): void
    {
        Log::error('Cart addition failed', [
            'error' => $e->getMessage(),
            'topic' => substr($this->topicText, 0, 50) . '...',
            'trace' => $e->getTraceAsString(),
        ]);
    }

    public function addToCart(): void
    {
        if ($this->mode === 'frequency_generator') {
            $this->addToCartFrequencyGenerator();
        } else {
            $this->addToCartOwnTopic();
        }
    }

    /**
     * Add to cart for own topic mode
     *
     * @return void
     */
    protected function addToCartOwnTopic(): void
    {
        if ($this->isTopicTextEmpty()) {
            $this->warnUser('no_product_title');
            return;
        }

        if ($this->hasExceededRateLimit()) {
            $this->warnUser('cart_max_allow_alert');
            return;
        }

        RateLimiter::hit($this->rateLimitKey(), 60);

        $this->ensureFrequencyIsSet();
        $this->ensureTimeSecondsIsSet();

        try {
            $response = $this->getFrequencyService()->submitToSajaxController($this->prepareCartData());

            $data = $response->getData(true);

            if ($data['success'] ?? false) {
                $this->successUser('added_successfully');
                $this->dispatch('cartUpdated');
            } else {
                $this->errorUser($data['message'] ?? 'cart_add_failed');
            }
        } catch (\Throwable $e) {
            $this->logCartError($e);
            $this->errorUser('cart_add_failed');
        }
    }

    /**
     * Add to cart for frequency generator mode
     *
     * @return void
     */
    protected function addToCartFrequencyGenerator(): void
    {
        try {
            // Validate inputs using dynamic rules
            $this->validate($this->getValidationRules());

            // Additional business logic validation
            $this->validateBusinessRules();

            // Rate limit cart additions (5 per minute)
            $key = 'cart_add_' . getUserId();
            if (RateLimiter::tooManyAttempts($key, 5)) {
                $seconds = RateLimiter::availableIn($key);
                throw new \Exception(trans('action.too_many_cart_additions', ['seconds' => $seconds]));
            }

            RateLimiter::hit($key, 60);

            $response = $this->getFrequencyService()->submitToSajaxController($this->prepareCartDataFrequencyGenerator());
            $this->handleCartResponse($response);

        } catch (ValidationException $e) {
            // Handle validation errors
            foreach ($e->errors() as $field => $messages) {
                foreach ($messages as $message) {
                    $this->addError($field, $message);
                }
            }
        } catch (\Throwable $e) {
            Log::error('Frequency generator cart addition failed', [
                'error' => $e->getMessage(),
                'user_id' => getUserId(),
                'data' => [
                    'topic' => substr($this->topicText, 0, 50) . '...',
                    'frequency' => $this->frequencyHz,
                    'time' => $this->frequencyTime
                ]
            ]);

            $this->showToastr('error', trans('action.frequency_generator'),
                $e->getMessage() ?: trans('action.error_occurred'));
        }
    }

    /**
     * Validate business rules for frequency generator mode
     *
     * @throws \Exception
     */
    protected function validateBusinessRules(): void
    {
        $validation = $this->getFrequencyService()->validateBusinessRules(
            $this->topicText,
            (int)$this->frequencyHz,
            (int)$this->frequencyTime,
            $this->biorythDetails
        );

        if (!$validation['valid']) {
            throw new \Exception(implode(', ', $validation['errors']));
        }
    }

    /**
     * Prepare cart data for frequency generator mode
     *
     * @return Request
     */
    protected function prepareCartDataFrequencyGenerator(): Request
    {
        return new Request([
            'ana_id'      => 1,
            'name'        => $this->getFrequencyService()->sanitizeInput($this->topicText),
            'submenu_id'  => '',
            'proID'       => '',
            'calculation' => '',
            'male'        => '',
            'heart'       => '',
            'price'       => (int)$this->frequencyTime,
            'causes_id'   => '',
            'medium_id'   => '',
            'tipp_id'     => '',
            'color'       => '',
            'type'        => 'Topic',
            'frequency'   => (int)$this->frequencyHz,
            'time'        => (int)$this->frequencyTime,
            '_token' => csrf_token()
        ]);
    }

    /**
     * Handle cart API response for frequency generator mode
     *
     * @param \Illuminate\Http\Client\Response $response
     * @throws \Exception
     */
    protected function handleCartResponse($response): void
    {
        $data = $response->getData(true);

        if ($data['success'] ?? false) {
            // Clear form and reset with new values for frequency generator mode
            $this->resetFormWithNewValues();

            // Show success notification
            $this->showToastr('success', trans('action.frequency_generator'),
                trans('action.topic_cart_save'));

            // Update cart UI
            $this->dispatch('cartUpdated');
        } else {
            $message = $data['message'] ?? trans('action.error_occurred');

            // Check for specific error types
            if (isset($data['error_code'])) {
                switch ($data['error_code']) {
                    case 'cart_full':
                        $message = trans('action.cart_max_allow_alert');
                        break;
                    case 'invalid_frequency':
                        $message = trans('action.invalid_frequency');
                        break;
                }
            }

            throw new \Exception($message);
        }
    }

    /**
     * Reset form with new random values for frequency generator mode
     *
     * @return void
     */
    protected function resetFormWithNewValues(): void
    {
        $this->reset(['topicText', 'frequencyHz']);

        // Generate new random time using FrequencyService
        $this->randomTime = $this->getFrequencyService()->generateRandomTime($this->biorythDetails);
        $this->frequencyTime = (string)$this->randomTime;
    }

    /**
     * Calculate harmonics for the current frequency
     *
     * @return void
     */
    public function calculateHarmonics(): void
    {
        if (empty($this->frequencyHz)) {
            $this->addError('frequencyHz', trans('action.frequency_placeholder'));
            return;
        }

        try {
            $frequency = (float)$this->frequencyHz;

            // Validate frequency range using FrequencyService
            if (!$this->getFrequencyService()->validateFrequency((int)$frequency)) {
                $this->addError('frequencyHz', trans('action.invalid_frequency'));
                return;
            }

            // Calculate harmonics using FrequencyService
            $harmonics = $this->getFrequencyService()->generateHarmonics($frequency);

            // Generate HTML table for modal using FrequencyService
            $harmonicsHtml = $this->getFrequencyService()->renderHarmonicsTable($harmonics);

            // Show in modal
            $this->showModal($harmonicsHtml, trans('action.calculate_harmonics'));

            // Log harmonic calculation for analytics
            Log::info('Harmonics calculated', [
                'frequency' => $frequency,
                'user_id' => getUserId()
            ]);

        } catch (\Exception $e) {
            Log::error('Harmonics calculation failed', [
                'error' => $e->getMessage(),
                'frequency' => $this->frequencyHz,
                'user_id' => getUserId()
            ]);

            $this->addError('frequencyHz', trans('action.harmonics_calculation_failed'));
        }
    }


    public function saveTopic()
    {
        try {
            $updated = DB::table('users')
                ->where('id', getUserId())
                ->update(['thema_speichern' => $this->topicText]);

            if ($updated) {
                $this->showToastr('success', trans('action.success'), trans('action.topic_saved_successfully'));
                // Update userDetails to reflect the change
                $this->userDetails = getUserDetails();
            } else {
                $this->showToastr('error', trans('action.error'), trans('action.topic_save_failed'));
            }
        } catch (\Exception $e) {
            Log::error('Topic save failed', [
                'error' => $e->getMessage(),
                'user_id' => getUserId()
            ]);
            $this->showToastr('error', trans('action.error'), trans('action.topic_save_failed'));
        }
    }

    public function deleteTopic()
    {
        try {
            $this->topicText = '';
            $updated = DB::table('users')
                ->where('id', getUserId())
                ->update(['thema_speichern' => '']);

            if ($updated) {
                $this->frequency = 0;
                $this->timeSeconds = 0;
                $this->timeDisplay = '';
                $this->showFrequency = false;
                $this->showTime = false;
                $this->showToastr('success', trans('action.success'), trans('action.topic_deleted_successfully'));
                // Update userDetails to reflect the change
                $this->userDetails = getUserDetails();
            } else {
                $this->showToastr('error', trans('action.error'), trans('action.topic_delete_failed'));
            }
        } catch (\Exception $e) {
            Log::error('Topic delete failed', [
                'error' => $e->getMessage(),
                'user_id' => getUserId()
            ]);
            $this->showToastr('error', trans('action.error'), trans('action.topic_delete_failed'));
        }
    }
    
    public function render()
    {
        return view('livewire.dashboard.widgets.navigation.own-topic');
    }
}
