<div>
    <style>
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .grid-stack-item-content,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .widget-body,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .custom-widget-container,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .custom-widget-body {
            overflow: visible !important;
        }

        /* Mode toggle styles */
        .mode-toggle-container .btn-group {
            border-radius: 0.375rem;
            overflow: hidden;
        }

        .mode-toggle-container .btn {
            border-radius: 0;
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
        }

        .mode-toggle-container .btn:first-child {
            border-top-left-radius: 0.375rem;
            border-bottom-left-radius: 0.375rem;
        }

        .mode-toggle-container .btn:last-child {
            border-top-right-radius: 0.375rem;
            border-bottom-right-radius: 0.375rem;
        }

        /* Frequency generator specific styles */
        .frequency-time-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .frequency-input-group,
        .time-input-group {
            flex: 1;
        }

        .harmonics-btn {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        /* Responsive design for smaller screens */
        @media (max-width: 576px) {
            .frequency-time-controls {
                flex-direction: column;
                gap: 15px;
            }

            .mode-toggle-container .btn {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
            }

            .mode-toggle-container .btn i {
                margin-right: 0.25rem;
            }
        }

        /* Ensure consistent spacing */
        .form-group.topic {
            margin-bottom: 1rem;
        }

        .topic-btns, .frequency-btns {
            margin-top: 1rem;
        }

        .topic-btns .btn, .frequency-btns .btn {
            margin: 0 0.25rem;
        }

        /* Enhanced input styling for frequency generator */
        .frequency-input-group .input-group,
        .time-input-group .input-group {
            border-radius: 0.375rem;
            overflow: hidden;
        }

        .frequency-input-group .form-control,
        .time-input-group .form-control {
            border-right: none;
        }

        .frequency-input-group .input-group-text,
        .time-input-group .input-group-text {
            background-color: #f8f9fa;
            border-left: none;
        }

        @keyframes glowInput {
            0%, 100% { box-shadow: 0 0 5px rgba(0, 123, 255, 0.5); }
            50% { box-shadow: 0 0 20px rgba(0, 123, 255, 0.8); }
        }

        @keyframes glowText {
            0%, 100% { color: #007bff; }
            50% { color: #0056b3; }
        }

        @keyframes glowButton {
            0%, 100% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.5); }
            50% { box-shadow: 0 0 15px rgba(255, 193, 7, 0.8); }
        }

        @keyframes glowPrimary {
            0%, 100% { box-shadow: 0 0 5px rgba(0, 123, 255, 0.5); }
            50% { box-shadow: 0 0 15px rgba(0, 123, 255, 0.8); }
        }

        .glowing-input {
            animation: glowInput 1.5s ease-in-out infinite;
        }

        .glowing-text {
            animation: glowText 1.5s ease-in-out infinite;
        }

        .glowing-button {
            animation: glowButton 1.5s ease-in-out infinite;
        }

        .btn-primary.glowing-button {
            animation: glowPrimary 1.5s ease-in-out infinite;
        }

        .position-relative {
            position: relative;
        }

        .position-absolute {
            z-index: 10;
        }
    </style>
    <link href="{{ asset('css/dashboard/right-panel.css') }}" rel="stylesheet">

    <!-- Mode Toggle Buttons -->
    <div class="mode-toggle-container mb-3">
        <div class="btn-group w-100" role="group">
            <button type="button"
                class="btn {{ $mode === 'own_topic' ? 'btn-primary' : 'btn-outline-primary' }}"
                wire:click="switchMode('own_topic')">
                <i class="fas fa-lightbulb"></i>
                {{ trans('action.topic_name') }}
            </button>
            <button type="button"
                class="btn {{ $mode === 'frequency_generator' ? 'btn-primary' : 'btn-outline-primary' }}"
                wire:click="switchMode('frequency_generator')">
                <i class="fas fa-wave-square"></i>
                {{ trans('action.frequency_generator') }}
            </button>
        </div>
    </div>

    <div class="topicTab fade show own-topic-text-div" id="nav-widget-topicTab-{{ $widget['id'] }}"
        style="background: #fff;background-clip: padding-box;">
        <div class="arrow"></div>
        @if($mode === 'own_topic')
            {{-- Own Topic Mode --}}
            <form wire:submit.prevent="">
                <div class="row">
                    <div class="col-6 d-flex align-items-center">
                        @if($showFrequency)
                        <small class="form-text text-muted">{{ $frequency }}</small>
                        <small class="form-text text-muted" style="margin-left: 2px;">{{trans('action.unit_hertz')}}</small>
                        @endif
                    </div>
                    <div class="col-6 d-flex align-items-center justify-content-end">
                        @if($showTime)
                        <small class="form-text text-muted text-right" style="color: #28a745;">{{ $timeDisplay }}</small>
                        <small class="form-text text-muted" style="margin-left: 2px;">{{trans('action.unit_seconds')}}</small>
                        @endif
                    </div>
                </div>
                <div class="form-group topic">
                    <textarea wire:model.blur="topicText"
                        wire:keyup.debounce.1000ms="calculateFrequencyIfNeeded"
                        rows="2"
                        class="form-control"
                        placeholder="{{trans('action.topic_name_placeholder')}}"></textarea>
                    @if($isCalculating)
                    <small class="form-text text-muted">{{trans('action.calculating_frequency')}}...</small>
                    @endif
                </div>
                <div class="text-center topic-btns">
                    @if(!$farbklang)
                    <button type="button" class="btn btn-primary icon"
                        wire:click="addToCart"
                        @if($isCalculating || empty($topicText)) disabled @endif>
                        {{trans('action.cart')}}
                    </button>
                    @endif
                    <button type="button" class="btn btn-success icon"
                        wire:click="saveTopic"
                        @if($isCalculating) disabled @endif>
                        {{trans('action.save')}}
                    </button>
                    <button type="button" class="btn btn-danger icon"
                        wire:click="deleteTopic"
                        @if($isCalculating) disabled @endif>
                        {{trans('action.delete')}}
                    </button>
                </div>
            </form>
        @else
            {{-- Frequency Generator Mode --}}
            <form wire:submit.prevent="addToCart">
                <div class="form-group topic">
                    <div class="position-relative">
                        <textarea rows="2" class="form-control" wire:model.live.debounce.500ms="topicText"
                            wire:loading.class="glowing-input" wire:target="topicText"
                            placeholder="{{ trans('action.topic_name_placeholder') }}"></textarea>
                        <div wire:loading wire:target="topicText" class="position-absolute top-0 end-0 p-2">
                            <small class="text-muted glowing-text">
                                <i class="fas fa-calculator"></i>
                            </small>
                        </div>
                    </div>
                    @error('topicText')
                    <small class="text-danger">{{ $message }}</small>
                    @enderror
                </div>
                <div class="frequency-time-controls">
                    <div class="frequency-input-group">
                        <label class="form-label small text-muted">{{ trans('action.frequency') }}</label>
                        <div class="input-group">
                            <input type="number" class="form-control" min="250"
                                max="20000" step="50" placeholder="{{ trans('action.frequency_placeholder') }}"
                                wire:model.defer="frequencyHz" wire:loading.class="glowing-input"
                                wire:target="topicText" wire:loading.attr="readonly" wire:target="topicText">
                            <span class="input-group-text" wire:loading.class="glowing-text"
                                wire:target="topicText">
                                {{ trans('action.unit_hertz') }}
                            </span>
                            <button type="button" class="btn btn-warning harmonics-btn"
                                wire:click="calculateHarmonics" wire:loading.attr="disabled"
                                wire:loading.class="glowing-button" wire:target="calculateHarmonics"
                                title="{{ __('action.calculate_harmonics') }}" data-bs-toggle="tooltip"
                                data-bs-placement="top">
                                <i class="fas fa-wave-square"></i>
                            </button>
                        </div>
                        @error('frequencyHz')
                        <small class="form-text text-danger">{{ $message }}</small>
                        @enderror
                    </div>
                    <div class="time-input-group">
                        <label class="form-label small text-muted">{{ trans('action.time') }}</label>
                        <div class="input-group">
                            <input type="number" class="form-control" min="5"
                                max="3600" step="1" placeholder="{{ trans('action.seconds_placeholder') }}"
                                wire:model.defer="frequencyTime" wire:loading.class="glowing-input"
                                wire:target="topicText" wire:loading.attr="readonly" wire:target="topicText">
                            <span class="input-group-text" wire:loading.class="glowing-text"
                                wire:target="topicText">{{ trans('action.unit_seconds') }}</span>
                        </div>
                        @error('frequencyTime')
                        <small class="form-text text-danger">{{ $message }}</small>
                        @enderror
                    </div>
                </div>
                <div class="text-center frequency-btns">
                    <button type="button" class="btn btn-primary icon" wire:click="addToCart"
                        wire:loading.attr="disabled" wire:loading.class="glowing-button" wire:target="addToCart">
                        <span>{{ trans('action.cart') }}</span>
                        <span wire:loading wire:target="addToCart" class="glowing-text">{{
                            trans('action.processing') }}...</span>
                    </button>
                </div>
            </form>
        @endif
    </div>
</div>
